"use client";

import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toStringBN } from "@/utils/helper";
import { TTradingWallet } from "@/types/balance.type";
import { useBalance } from "./useBalance";
import { useRaidenxWallet } from "./useRaidenxWallet";
import rf from "@/services/RequestFactory";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { useNetwork } from "@/context";
import { WHYPE_ADDRESS, ZERO_ADDRESS } from "@/constants";
import Storage from "@/libs/storage";

export const useTradingWallet = (
  addressTokenBase: string,
  addressTokenQuoteSelected: string
) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const balances = useSelector((state: RootState) => state.user.balances);
  const [quoteBalances, setQuoteBalances] = useState<any[]>([]);
  const { activeWalletAddresses } = useRaidenxWallet();
  const quoteAddressRef = useRef<string>(addressTokenQuoteSelected);
  const { currentNetwork } = useNetwork();
  const accessToken = Storage.getAccessToken();

  useEffect(() => {
    if (!addressTokenQuoteSelected) {
      return;
    }
    quoteAddressRef.current = addressTokenQuoteSelected;
  }, [addressTokenQuoteSelected]);

  const [tradingWallets, setTradingWallets] = useState<TTradingWallet[]>([]);
  const { getWalletBalance } = useBalance();

  const activeTradingWallets = useMemo(() => {
    if (!activeWalletAddresses?.length) {
      return tradingWallets.slice(0, 1);
    }

    return tradingWallets.filter((item) =>
      activeWalletAddresses.includes(item.address)
    );
  }, [activeWalletAddresses, tradingWallets]) as TTradingWallet[];

  const fetchQuoteBalances = useCallback(async () => {
    let _addressTokenQuoteSelected = addressTokenQuoteSelected;
    let _quoteAddress = quoteAddressRef.current;

    if (addressTokenQuoteSelected === WHYPE_ADDRESS) {
      _addressTokenQuoteSelected = ZERO_ADDRESS; // HYPE is the native token of Hype network
      _quoteAddress = ZERO_ADDRESS;
    }

    const res = await rf
      .getRequest("TokenRequest")
      .getBalanceOf(currentNetwork, _addressTokenQuoteSelected);
    console.log({ _quoteAddress, res }, "wallet data");
    setQuoteBalances(
      res.filter((item: any) => item.tokenAddress === _quoteAddress)
    );
  }, [addressTokenQuoteSelected, accessToken]);

  useEffect(() => {
    if (!addressTokenQuoteSelected) {
      setQuoteBalances([]);
      return;
    }
    fetchQuoteBalances();
  }, [addressTokenQuoteSelected, fetchQuoteBalances]);

  useEffect(() => {
    const handleUpdateQuoteBalanceIfNeeded = (event: TBroadcastEvent) => {
      const data = event.detail;
      if (data.token.address === quoteAddressRef.current) {
        fetchQuoteBalances();
      }
    };
    AppBroadcast.on(
      BROADCAST_EVENTS.WALLET_UPDATED,
      handleUpdateQuoteBalanceIfNeeded
    );
    AppBroadcast.on(BROADCAST_EVENTS.ORDER_UPDATED, fetchQuoteBalances);
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.WALLET_UPDATED,
        handleUpdateQuoteBalanceIfNeeded
      );
      AppBroadcast.remove(BROADCAST_EVENTS.ORDER_UPDATED, fetchQuoteBalances);
    };
  }, [fetchQuoteBalances, addressTokenQuoteSelected, accessToken]);

  useEffect(() => {
    if (!wallets.length) return;

    (async () => {
      setTradingWallets(
        await Promise.all(
          wallets?.map(async (wallet) => {
            const tokenBaseBalance = getWalletBalance(
              addressTokenBase,
              wallet.address
            );
            const tokenQuoteBalance =
              quoteBalances?.find(
                (item: any) => item.walletAddress === wallet.address
              )?.balance || "0";
            console.log(tokenQuoteBalance, "tokenQuoteBalance");
            return {
              ...wallet,
              nativeBalance: toStringBN(wallet.balance),
              baseBalance: toStringBN(tokenBaseBalance),
              quoteBalance: toStringBN(tokenQuoteBalance || "0"),
            };
          })
        )
      );
    })();
  }, [
    balances,
    wallets?.length,
    addressTokenBase,
    addressTokenQuoteSelected,
    quoteBalances,
  ]);

  const activeTotalQuoteBalance = useMemo(
    () =>
      activeTradingWallets.reduce((sum, obj) => sum + +obj?.quoteBalance, 0),
    [activeTradingWallets]
  );

  const activeTotalBaseBalance = useMemo(
    () => activeTradingWallets.reduce((sum, obj) => sum + +obj?.baseBalance, 0),
    [activeTradingWallets]
  );

  const totalQuoteBalance = useMemo(
    () => tradingWallets.reduce((sum, obj) => sum + +obj?.quoteBalance, 0),
    [tradingWallets]
  );

  const totalBaseBalance = useMemo(
    () => tradingWallets.reduce((sum, obj) => sum + +obj?.baseBalance, 0),
    [tradingWallets]
  );

  return {
    tradingWallets,
    activeTradingWallets,
    activeTotalQuoteBalance,
    activeTotalBaseBalance,
    totalQuoteBalance,
    totalBaseBalance,
  };
};
