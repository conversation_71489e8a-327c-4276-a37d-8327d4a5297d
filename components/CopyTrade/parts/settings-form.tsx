import React, { useMemo, useState, useEffect } from "react";
import { NumericFormat } from "react-number-format";
import { useDispatch, useSelector } from "react-redux";
import { CloseIcon, CoinTip, GasIcon, SettingsIcon } from "@/assets/icons";
import { AppLogoNetwork, AppToggle } from "@/components";
import { toastError, toastSuccess } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { ISettingsBuy } from "@/types/copytrade.type";
import { formatNumber } from "@/utils/format";
import { calculateMaxGasFee } from "@/utils/helper";
import BigNumber from "bignumber.js";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { NETWORKS } from "@/utils/contants";
import { AppDispatch } from "@/store/index";
import { getSettingsCopyTradeOrder } from "@/store/user.store";
import { useNetwork } from "@/context";
import {
  validateOrderSettings,
  getGasPriceUnit,
  getGasPriceValidationMessage,
} from "@/utils/orderSettingsValidator";

export const SettingsForm = ({
  onCloseSettings,
  settingsBuy,
  setSettingsBuy,
}: // settingsSell,
// setSettingsSell,
{
  onCloseSettings: () => void;
  settingsBuy: ISettingsBuy;
  setSettingsBuy: (value: ISettingsBuy) => void;
  // settingsSell: ISettingsSell;
  // setSettingsSell: (value: ISettingsSell) => void;
}) => {
  // const [type, setType] = useState<TRADE_TYPE>(TRADE_TYPE.BUY);

  const [isEnableCopyTradeTip, setIsEnableCopyTradeTip] =
    useState<boolean>(false);
  const [tipAmount, setTipAmount] = useState<any>("0.02");
  const [slippage, setSlippage] = useState<any>("");
  const [gasPrice, setGasPrice] = useState<any>("750");
  const { currentNetwork } = useNetwork();

  const maxGasFeeCopyTrade = useMemo(
    () => calculateMaxGasFee(gasPrice || 750),
    [gasPrice]
  );

  const settingsCopyTradeOrder = useSelector(
    (state: RootState) => state.user.settingsCopyTradeOrder
  );
  const network = useSelector((state: RootState) => state.user.network);
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (new BigNumber(tipAmount || 0).gt(0)) {
      setIsEnableCopyTradeTip(true);
      return;
    }
    setIsEnableCopyTradeTip(false);
  }, [tipAmount]);

  useEffect(() => {
    setTipAmount(settingsCopyTradeOrder?.tipAmount);
    setSlippage(settingsCopyTradeOrder?.slippage);
    setGasPrice(settingsCopyTradeOrder?.gasPrice);
  }, [settingsCopyTradeOrder]);

  const type = TRADE_TYPE.BUY;

  const onSave = async () => {
    if (
      settingsBuy.minMCCopy &&
      settingsBuy.maxMCCopy &&
      +settingsBuy.minMCCopy >= +settingsBuy.maxMCCopy
    ) {
      toastError("Error", "Min MC copy must be less than Max MC copy");
      return;
    }

    const validation = validateOrderSettings(currentNetwork, {
      gasPrice,
      tipAmount,
      slippage,
      isEnableTip: isEnableCopyTradeTip,
    });

    if (!validation.isValid) {
      validation.errors.forEach((error) => {
        toastError("Error", error);
      });
      return;
    }

    try {
      await rf
        .getRequest("PresetSettingRequest")
        .updateCopyTradeOrderSettings(network, {
          ...settingsCopyTradeOrder,
          tipAmount: isEnableCopyTradeTip ? +tipAmount : 0,
          slippage: +slippage,
          gasPrice: +gasPrice,
        });

      await rf.getRequest("CopyTradeRequest").updateSettings(currentNetwork, {
        limitSuiPerBuy: settingsBuy.maxBuy ?? undefined,
        targetLiquidityMin: settingsBuy.minLPCopy ?? undefined,
        targetMarketCapMin: settingsBuy.minMCCopy ?? undefined,
        targetMarketCapMax: settingsBuy.maxMCCopy ?? undefined,
      });

      onCloseSettings();
      dispatch(getSettingsCopyTradeOrder({ network }));
      toastSuccess("Success", "Update successfully!");
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  return (
    <div className="customer-scroll tablet:pr-[8px] h-full w-full overflow-auto px-[8px] py-[12px]">
      <div className="mb-[12px] flex items-center justify-between">
        <div className="body-md-regular-14 flex gap-2">
          <SettingsIcon className="h-[18px] w-[18px]" />
          Copy Trade Settings
        </div>

        <div
          className="text-neutral-alpha-500 hover:text-neutral-alpha-1000 cursor-pointer"
          onClick={onCloseSettings}
        >
          <CloseIcon className="h-[12px] w-[12px]" />
        </div>
      </div>

      {/* <div className="grid grid-cols-2 mb-[8px] mt-[12px] bg-neutral-beta-500 p-[4px] rounded-[4px]">
        <div
          className={`${
            type === TRADE_TYPE.BUY
              ? 'bg-green-900 text-green-500'
              : 'text-neutral-alpha-500'
          } flex items-center gap-1 justify-center text-center px-[8px] py-[6px] body-sm-semibold-14 cursor-pointer rounded-[4px]`}
          onClick={() => setType(TRADE_TYPE.BUY)}
        >
          <BuyIcon /> Buy
        </div>
        <div
          className={`${
            type === TRADE_TYPE.SELL
              ? 'bg-red-900 text-red-500'
              : 'text-neutral-alpha-500'
          } flex items-center gap-1 justify-center text-center px-[8px] py-[6px] body-sm-semibold-14 cursor-pointer rounded-[4px]`}
          onClick={() => setType(TRADE_TYPE.SELL)}
        >
          <TagIcon /> Sell
        </div>
      </div> */}

      <div className="mb-6 flex flex-col gap-2">
        {type === TRADE_TYPE.BUY && (
          <>
            {/* <div>
              <div className="flex items-center gap-1 body-sm-regular-12 text-neutral-alpha-500 mb-2">
                 Spending Limit
              </div>

              <div className="flex items-center gap-2 border border-neutral-alpha-50 rounded-[4px] p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  $
                </div>
                <NumericFormat
                  value={settingsBuy.spendingLimit ?? ''}
                  allowLeadingZeros
                  allowNegative={false}
                  placeholder="Limit SUI spend to buy a token"
                  thousandSeparator=","
                  className="bg-transparent outline-none body-md-semibold-14 w-full text-left leading-[18px] text-white-1000 placeholder:text-[12px]
                  placeholder:text-white-200 focus:outline-none bg-black-900 flex-1 placeholder:font-normal"
                  decimalScale={8}
                  onValueChange={({ floatValue }) => {
                    return setSettingsBuy({
                      ...settingsBuy,
                      spendingLimit: floatValue?.toString() ?? '',
                    });
                  }}
                />
              </div>
            </div>{' '} */}
            <span className="text-white-500 border-neutral-alpha-50  w-full border-b border-solid pb-[4px] text-[14px]">
              Buy Settings
            </span>
            <div>
              <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
                Max buy
              </div>

              <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[4px] border p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  <AppLogoNetwork
                    network={NETWORKS.SUI}
                    isBase
                    className="h-[12px] w-[12px]"
                  />
                </div>
                <NumericFormat
                  value={settingsBuy.maxBuy ?? ""}
                  allowLeadingZeros
                  allowNegative={false}
                  placeholder="Max SUI per buy"
                  thousandSeparator=","
                  className="body-md-semibold-14 text-white-1000 placeholder:text-white-200 bg-black-900 w-full flex-1 bg-transparent text-left
                leading-[18px] outline-none placeholder:text-[12px] placeholder:font-normal focus:outline-none"
                  decimalScale={8}
                  onValueChange={({ floatValue }) => {
                    return setSettingsBuy({
                      ...settingsBuy,
                      maxBuy: floatValue,
                    });
                  }}
                />
              </div>
            </div>
            <div>
              <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
                Min LP copy
              </div>

              <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[4px] border p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  $
                </div>
                <NumericFormat
                  value={settingsBuy.minLPCopy ?? ""}
                  allowLeadingZeros
                  allowNegative={false}
                  placeholder="Minimum Liquidity Pool copy"
                  thousandSeparator=","
                  className="body-md-semibold-14 text-white-1000 placeholder:text-white-200 bg-black-900 w-full flex-1 bg-transparent text-left
                leading-[18px] outline-none placeholder:text-[12px] placeholder:font-normal focus:outline-none"
                  decimalScale={8}
                  onValueChange={({ floatValue }) => {
                    return setSettingsBuy({
                      ...settingsBuy,
                      minLPCopy: floatValue,
                    });
                  }}
                />
              </div>
            </div>
            <div>
              <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
                Min MC copy
              </div>

              <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[4px] border p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  $
                </div>
                <NumericFormat
                  value={settingsBuy.minMCCopy ?? ""}
                  allowLeadingZeros
                  allowNegative={false}
                  placeholder="Minimum Market Cap copy"
                  thousandSeparator=","
                  className="body-md-semibold-14 text-white-1000 placeholder:text-white-200 bg-black-900 w-full flex-1 bg-transparent text-left
                leading-[18px] outline-none placeholder:text-[12px] placeholder:font-normal focus:outline-none"
                  decimalScale={8}
                  onValueChange={({ floatValue }) => {
                    return setSettingsBuy({
                      ...settingsBuy,
                      minMCCopy: floatValue,
                    });
                  }}
                />
              </div>
            </div>
            <div>
              <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
                Max MC copy
              </div>

              <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[4px] border p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  $
                </div>
                <NumericFormat
                  value={settingsBuy.maxMCCopy ?? ""}
                  allowLeadingZeros
                  allowNegative={false}
                  placeholder="Maximum Market Cap copy"
                  thousandSeparator=","
                  className="body-md-semibold-14 text-white-1000 placeholder:text-white-200 bg-black-900 w-full flex-1 bg-transparent text-left
                leading-[18px] outline-none placeholder:text-[12px] placeholder:font-normal focus:outline-none"
                  decimalScale={8}
                  onValueChange={({ floatValue }) => {
                    return setSettingsBuy({
                      ...settingsBuy,
                      maxMCCopy: floatValue,
                    });
                  }}
                />
              </div>
            </div>
          </>
        )}

        <span className="text-white-500 border-neutral-alpha-50  w-full border-b border-solid pb-[4px] text-[14px]">
          General Settings
        </span>
        <div>
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            Slippage Limit
          </div>

          <div className="border-neutral-alpha-50 mb-2 flex items-center gap-2 rounded-[4px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">%</div>
            <NumericFormat
              value={slippage ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setSlippage(floatValue);
              }}
            />
          </div>

          <div className="mb-2">
            <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
              <GasIcon /> Gas Price
            </div>

            <div className="text-white-700 body-xs-regular-10 mb-2">
              {getGasPriceValidationMessage(currentNetwork)}
            </div>

            <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
              <div className="body-sm-regular-12 text-neutral-alpha-500">
                {getGasPriceUnit(currentNetwork)}
              </div>
              <NumericFormat
                value={gasPrice ?? ""}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent outline-none"
                decimalScale={0}
                onValueChange={({ floatValue }) => {
                  setGasPrice(floatValue);
                }}
              />
            </div>

            <div className="item-center flex justify-between">
              <div className="body-xs-regular-10 text-white-700 mt-2">
                Est Gas Fee:
              </div>
              <div className="text-white-1000 body-xs-medium-10 mt-2">
                ~{formatNumber(maxGasFeeCopyTrade)} SUI
              </div>
            </div>
          </div>
          <div className="mb-2">
            <div className="flex items-center justify-between">
              <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
                <CoinTip /> Tip Amount
              </div>
              <AppToggle
                value={isEnableCopyTradeTip}
                onChange={() => {
                  if (!isEnableCopyTradeTip) {
                    setTipAmount("0.02");
                  }
                  setIsEnableCopyTradeTip(!isEnableCopyTradeTip);
                }}
              />
            </div>

            <div className="body-xs-regular-10 text-white-700 mb-2">
              The amount you send to validators to pick up your transaction
              faster. Minimum is 0.02 SUI.
            </div>

            {isEnableCopyTradeTip && (
              <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
                <div className="body-sm-regular-12 text-neutral-alpha-500">
                  <AppLogoNetwork
                    network={currentNetwork}
                    isBase
                    className="h-[12px] w-[12px]"
                  />
                </div>
                <NumericFormat
                  value={tipAmount ?? ""}
                  allowLeadingZeros
                  allowNegative={false}
                  thousandSeparator=","
                  className="body-md-semibold-14 w-full bg-transparent outline-none"
                  decimalScale={6}
                  onValueChange={({ floatValue }) => {
                    setTipAmount(floatValue);
                  }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <div
        onClick={onSave}
        className="text-neutral-beta-900 action-sm-medium-14 bg-neutral-alpha-1000 my-2 flex cursor-pointer items-center justify-center gap-1 rounded-[6px] px-2 py-[10px]"
      >
        Save Settings
      </div>
    </div>
  );
};
