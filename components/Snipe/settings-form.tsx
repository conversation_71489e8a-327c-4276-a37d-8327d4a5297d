"use client";
import * as React from "react";
import { useEffect, useState, useMemo } from "react";
import { NumericFormat } from "react-number-format";
import {
  CloseIcon,
  CoinTip,
  GasIcon,
  Setting,
  SettingsIcon,
  SlippageIcon,
} from "@/assets/icons";
import Storage from "@/libs/storage";
import { toastError, toastSuccess } from "@/libs/toast";
import { AppLogoNetwork, AppToggle } from "..";
import { RootState } from "@/store";
import { useDispatch, useSelector } from "react-redux";
import BigNumber from "bignumber.js";
import { formatNumber } from "@/utils/format";
import { calculateMaxGasFeeForNetwork } from "@/utils/helper";
import rf from "@/services/RequestFactory";
import { AppDispatch } from "@/store/index";
import { getSettingsSnipeOrder } from "@/store/user.store";
import { useNetwork } from "@/context";
import {
  validateOrderSettings,
  getGasPriceUnit,
  getGasPriceValidationMessage,
  getTipAmountValidationMessage,
} from "@/utils/orderSettingsValidator";
import { getNetworkSymbol } from "@/app/providers/networkChains";

export const SettingsForm = ({
  onCloseSettings,
}: {
  onCloseSettings: () => void;
}) => {
  const [tipAmount, setTipAmount] = useState<any>("0.02");
  const [slippage, setSlippage] = useState<any>("");
  const [gasPrice, setGasPrice] = useState<any>("750");
  const [isEnableTip, setIsEnableTip] = useState<boolean>(true);
  const [defaultSnipeAmount, setDefaultSnipeAmount] = useState<any>([]);

  const settingsSnipeOrder = useSelector(
    (state: RootState) => state.user.settingsSnipeOrder
  );
  const { currentNetwork } = useNetwork();

  const maxGasFeeSnipe = useMemo(() => {
    return calculateMaxGasFeeForNetwork(gasPrice, currentNetwork);
  }, [gasPrice, currentNetwork]);

  const snipeSettings = Storage.getSnipeSettings();
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    setDefaultSnipeAmount(snipeSettings.defaultSnipeAmount);
  }, []);

  useEffect(() => {
    if (new BigNumber(settingsSnipeOrder?.tipAmount).gt(0)) {
      setIsEnableTip(true);
      return;
    }

    setIsEnableTip(false);
  }, [settingsSnipeOrder]);

  useEffect(() => {
    setTipAmount(settingsSnipeOrder?.tipAmount);
    setSlippage(settingsSnipeOrder?.slippage);
    setGasPrice(settingsSnipeOrder?.gasPrice);
  }, [settingsSnipeOrder]);

  const onSave = async () => {
    const validation = validateOrderSettings(currentNetwork, {
      gasPrice,
      tipAmount,
      slippage,
      isEnableTip,
    });

    if (!validation.isValid) {
      validation.errors.forEach((error) => {
        toastError("Error", error);
      });
      return;
    }

    try {
      Storage.setSnipeSettings({
        ...snipeSettings,
        defaultSnipeAmount,
      });

      await rf
        .getRequest("PresetSettingRequest")
        .updateSnipeOrderSettings(currentNetwork, {
          ...settingsSnipeOrder,
          tipAmount: isEnableTip ? +tipAmount : 0,
          slippage: +slippage,
          gasPrice: +gasPrice,
        });

      onCloseSettings();
      dispatch(getSettingsSnipeOrder({ network: currentNetwork }));
      toastSuccess("Success", "Update successfully!");
    } catch (e: any) {
      console.error(e);
      toastError("Error", e.message || "Something went wrong!");
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="body-md-regular-14 mb-3 flex gap-2">
          <SettingsIcon className="h-[18px] w-[18px]" />
          Snipe Settings
        </div>

        <div
          className="text-neutral-alpha-500 hover:text-neutral-alpha-1000 cursor-pointer"
          onClick={onCloseSettings}
        >
          <CloseIcon className="h-[12px] w-[12px]" />
        </div>
      </div>
      <div className="mb-6 flex flex-col gap-4">
        <div>
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            <SlippageIcon />
            <span className="text-white-700">Slippage Limit</span>
          </div>

          <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">%</div>
            <NumericFormat
              value={slippage ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setSlippage(floatValue);
              }}
            />
          </div>
        </div>

        <div>
          <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
            <GasIcon /> <span className="text-white-700">Gas Price</span>
          </div>

          <div className="text-white-700 body-xs-regular-10 mb-2">
            {getGasPriceValidationMessage(currentNetwork)}
          </div>

          <div className="border-neutral-alpha-50 flex items-center gap-2 rounded-[6px] border p-2">
            <div className="body-sm-regular-12 text-neutral-alpha-500">
              {getGasPriceUnit(currentNetwork)}
            </div>
            <NumericFormat
              value={gasPrice ?? ""}
              allowLeadingZeros
              allowNegative={false}
              thousandSeparator=","
              className="body-md-semibold-14 w-full bg-transparent outline-none"
              decimalScale={2}
              onValueChange={({ floatValue }) => {
                return setGasPrice(floatValue);
              }}
            />
          </div>

          <div className="item-center flex justify-between">
            <div className="body-xs-regular-10 text-white-700 mt-1">
              Est Gas Fee:
            </div>
            <div className="text-white-1000 body-xs-medium-10 mt-1">
              ~{formatNumber(maxGasFeeSnipe)} {getNetworkSymbol(currentNetwork)}
            </div>
          </div>
        </div>

        <div>
          <div className="flex items-center justify-between">
            <div className="body-sm-regular-12 text-neutral-alpha-500 mb-2 flex items-center gap-1">
              <CoinTip /> <span className="text-white-700">Tip Amount</span>
            </div>
            <AppToggle
              value={isEnableTip}
              onChange={() => {
                if (!isEnableTip && !+tipAmount) {
                  setTipAmount("0.02");
                }
                setIsEnableTip(!isEnableTip);
              }}
            />
          </div>

          <div className="body-xs-regular-10 text-white-700">
            {getTipAmountValidationMessage(currentNetwork)}
          </div>

          {isEnableTip && (
            <div className="border-neutral-alpha-50 mt-4 flex items-center gap-2 rounded-[4px] border p-2">
              <div className="body-sm-regular-12 text-neutral-alpha-500">
                <AppLogoNetwork
                  network={currentNetwork}
                  isBase
                  className="h-[12px] w-[12px]"
                />
              </div>
              <NumericFormat
                value={tipAmount ?? ""}
                allowLeadingZeros
                allowNegative={false}
                thousandSeparator=","
                className="body-md-semibold-14 w-full bg-transparent outline-none"
                decimalScale={6}
                onValueChange={({ floatValue }) => {
                  return setTipAmount(floatValue);
                }}
              />
            </div>
          )}
        </div>

        <div>
          <div className="body-sm-regular-12 text-white-700 mb-2 flex items-center gap-1">
            <Setting /> Custom Snipe Amount
          </div>
          <div className="grid grid-cols-4 gap-2">
            {[...Array(4)].map((_, index: number) => {
              return (
                <div
                  className="border-neutral-alpha-50 gap-2 rounded-[4px] border p-2 text-center"
                  key={index}
                >
                  <NumericFormat
                    value={defaultSnipeAmount[index] ?? ""}
                    allowLeadingZeros
                    allowNegative={false}
                    thousandSeparator=","
                    className="body-md-semibold-14 w-full bg-transparent outline-none"
                    decimalScale={0}
                    onValueChange={({ floatValue }) => {
                      return setDefaultSnipeAmount((prevState: any) => {
                        prevState[index] = floatValue;
                        return prevState;
                      });
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div
        onClick={onSave}
        className="text-neutral-beta-900 action-sm-medium-14 bg-neutral-alpha-1000 my-2 flex cursor-pointer items-center justify-center gap-1 rounded-[6px] px-2 py-[10px]"
      >
        Save Settings
      </div>
    </div>
  );
};
