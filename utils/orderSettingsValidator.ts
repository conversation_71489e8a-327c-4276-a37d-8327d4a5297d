import BigNumber from "bignumber.js";
import {
  getNetworkGasPriceConfig,
  getNetworkTipAmountConfig,
  getNetworkSymbol,
} from "@/app/providers/networkChains";

export interface OrderSettingsValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface OrderSettingsValidationInput {
  gasPrice: number | string;
  tipAmount: number | string;
  slippage?: number | string;
  isEnableTip?: boolean;
}

export const validateOrderSettings = (
  network: string,
  settings: OrderSettingsValidationInput
): OrderSettingsValidationResult => {
  const errors: string[] = [];
  const gasPriceConfig = getNetworkGasPriceConfig(network);
  const tipAmountConfig = getNetworkTipAmountConfig(network);
  const networkSymbol = getNetworkSymbol(network);

  if (gasPriceConfig) {
    const gasPrice = new BigNumber(settings.gasPrice || 0);
    if (gasPrice.lt(gasPriceConfig.minimum) || gasPrice.isNaN()) {
      errors.push(
        `Minimum gas price is ${gasPriceConfig.minimum} ${gasPriceConfig.unit}`
      );
    }
  }

  if (settings.isEnableTip !== false && tipAmountConfig) {
    const tipAmount = new BigNumber(settings.tipAmount || 0);
    if (tipAmount.lt(tipAmountConfig.minimum) || tipAmount.isNaN()) {
      errors.push(
        `Minimum tip amount is ${tipAmountConfig.minimum} ${networkSymbol}`
      );
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const getDefaultOrderSettings = (network: string) => {
  const gasPriceConfig = getNetworkGasPriceConfig(network);
  const tipAmountConfig = getNetworkTipAmountConfig(network);

  return {
    gasPrice: gasPriceConfig?.default || 750,
    tipAmount: tipAmountConfig?.default || 0.02,
    slippage: 10,
  };
};

export const getGasPriceUnit = (network: string): string => {
  const gasPriceConfig = getNetworkGasPriceConfig(network);
  return gasPriceConfig?.unit || "MIST";
};

export const getMinimumGasPrice = (network: string): number => {
  const gasPriceConfig = getNetworkGasPriceConfig(network);
  return gasPriceConfig?.minimum || 750;
};

export const getMinimumTipAmount = (network: string): number => {
  const tipAmountConfig = getNetworkTipAmountConfig(network);
  return tipAmountConfig?.minimum || 0.02;
};

export const getGasPriceValidationMessage = (network: string): string => {
  const gasPriceConfig = getNetworkGasPriceConfig(network);
  if (!gasPriceConfig) {
    return "Enter gas price in MIST to estimate the gas fee for your transaction. Min is 750 MIST";
  }

  return `Enter gas price in ${gasPriceConfig.unit} to estimate the gas fee for your transaction. Min is ${gasPriceConfig.minimum} ${gasPriceConfig.unit}`;
};

export const getTipAmountValidationMessage = (network: string): string => {
  const tipAmountConfig = getNetworkTipAmountConfig(network);
  const networkSymbol = getNetworkSymbol(network);

  if (!tipAmountConfig) {
    return `Minimum tip amount is 0.02 ${networkSymbol}`;
  }

  return `Minimum tip amount is ${tipAmountConfig.minimum} ${networkSymbol}`;
};
