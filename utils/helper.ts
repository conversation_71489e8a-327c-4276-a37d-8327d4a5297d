import config from "@/config";
import BigNumber from "bignumber.js";
import {
  NETWORKS,
  SUI_DECIMALS,
  getNativeTokenForNetwork,
} from "@/utils/contants";
import copy from "copy-to-clipboard";
import { toastError, toastSuccess } from "../libs/toast";
import moment from "moment";

export const getLinkAddressExplorer = (network: string, address: string) => {
  if (!address) return "#";

  if (network === NETWORKS.SUI) {
    return `${config.networks?.sui?.explorerUrl}/address/${address}?utm_source=raidenx&utm_medium=web`;
  }

  // TODO: Add explorer URLs for HYPE and SOMI networks
  if (network === NETWORKS.HYPEREVM) {
    return "#"; // Replace with actual HYPE explorer URL when available
  }

  if (network === NETWORKS.SOMNIA) {
    return "#"; // Replace with actual SOMI explorer URL when available
  }

  return "#";
};

export const getLinkTokenExplorer = (network: string, address: string) => {
  if (!address) return "#";

  if (network === NETWORKS.SUI) {
    return `${config.networks?.sui?.explorerUrl}/coin/${address}?utm_source=raidenx&utm_medium=web`;
  }

  // TODO: Add explorer URLs for HYPE and SOMI networks
  if (network === NETWORKS.HYPEREVM) {
    return "#"; // Replace with actual HYPE explorer URL when available
  }

  if (network === NETWORKS.SOMNIA) {
    return "#"; // Replace with actual SOMI explorer URL when available
  }

  return "#";
};

export const getLinkTxExplorer = (
  network: string,
  version: number | string
) => {
  if (!version && network === NETWORKS.SUI) return "#";

  if (network === NETWORKS.SUI) {
    return `${config.networks?.sui?.explorerUrl}/txblock/${version}?utm_source=raidenx&utm_medium=web`;
  }

  if (network === NETWORKS.HYPEREVM) {
    return `https://hyperevmscan.xyz`;
  }

  if (network === NETWORKS.SOMNIA) {
    return "#";
  }

  return "#";
};

export const filterParams = (params: any) => {
  return Object.fromEntries(Object.entries(params).filter(([_, v]) => v));
};

export const isZero = (a: number | string | BigNumber | null | undefined) => {
  return new BigNumber(a || 0).isZero();
};

export const abs = (a: number | string | BigNumber) => {
  return new BigNumber(a).abs();
};

export const toStringBN = (a: number | string | BigNumber | undefined) => {
  return new BigNumber(a || 0).toString();
};

export const minusBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber
) => {
  return new BigNumber(a || 0).minus(b || 0).toString();
};

export const compareBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber
) => {
  return new BigNumber(a || 0).comparedTo(b || 0);
};

export const isGreaterBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber
) => {
  return new BigNumber(a || 0).comparedTo(b || 0) > 0;
};

export const plusBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber
) => {
  return new BigNumber(a || 0).plus(b || 0).toString();
};

export const multipliedBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber,
  decimals = 15
) => {
  return new BigNumber(
    new BigNumber(a || 0).multipliedBy(b || 0).toFixed(decimals)
  ).toString();
};

export const dividedBN = (
  a: number | string | BigNumber,
  b: number | string | BigNumber
) => {
  return new BigNumber(a || 0).dividedBy(b || 0).toString();
};

export const convertUnit2Real = (
  val: number | string | BigNumber,
  decimals: number | string | BigNumber
) => {
  return new BigNumber(val)
    .dividedBy(new BigNumber(10).pow(decimals || 0))
    .toString();
};

export const calculateMaxGasFee = (gasPrice: number): number => {
  return 0.01 * (gasPrice / 750);
};

export const calculateMaxGasFeeForNetwork = (
  gasPrice: number,
  network: NETWORKS
): number => {
  switch (network) {
    case NETWORKS.SUI:
      return 0.01 * (gasPrice / 750);
    case NETWORKS.HYPEREVM:
      return 0.001 * (gasPrice / 20);
    case NETWORKS.SOMNIA:
      return 0.001 * (gasPrice / 20);
    default:
      return 0.01 * (gasPrice / 750);
  }
};

export const sleep = async (ms: number) =>
  new Promise((r) => setTimeout(r, ms));

export const get24hPreviousSeconds = () => {
  const now = new Date();
  return Math.floor(now.getTime() / 1000) - 24 * 60 * 60;
};

export const get1WeekPreviousSeconds = () => {
  const now = new Date();
  return Math.floor(now.getTime() / 1000) - 7 * 24 * 60 * 60;
};

export const getSymbolTokenNative = (network: string) => {
  const nativeToken = getNativeTokenForNetwork(network);
  return nativeToken.symbol;
};

export const isValidSuiAddress = (address: string) => {
  const suiAddressRegex = /^0x[0-9a-fA-F]{64}$/;
  return suiAddressRegex.test(address);
};

export const isValidCAAddress = (address: string) => {
  const CAAddressRegex = /^0x[a-fA-F0-9]{60,64}::[a-zA-Z0-9_]+::[a-zA-Z0-9_]+$/;
  return CAAddressRegex.test(address);
};

export const isMobile = () => {
  if (typeof window !== "undefined") {
    return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
  }
  return false;
};

export const isAndroidMobile = () => {
  if (typeof window !== "undefined") {
    return /Android/i.test(navigator.userAgent);
  }
  return false;
};

export const isIOSMobile = () => {
  if (typeof window !== "undefined") {
    return /iPhone|iPad|iPod/i.test(navigator.userAgent);
  }
  return false;
};

export const copyToClipboard = (message: string) => {
  try {
    copy(message);
    toastSuccess("Success", "Copied!");
  } catch (error: any) {
    toastError("Error", error.message || "Something went wrong!");
  }
};

export const toDecimals = (
  value: number | string | BigNumber,
  decimals: number
) => {
  return new BigNumber(value)
    .multipliedBy(new BigNumber(10).pow(decimals))
    .toString();
};

export const toDecimalsSui = (value: number | string | BigNumber) => {
  return toDecimals(value, SUI_DECIMALS);
};

export const toDecimalsForNetwork = (
  value: number | string | BigNumber,
  network: string
) => {
  const nativeToken = getNativeTokenForNetwork(network);
  return toDecimals(value, nativeToken.decimals);
};

export const convertDecToMist = (
  value: number | string | BigNumber,
  decimals: number = SUI_DECIMALS
) => {
  return new BigNumber(value)
    .multipliedBy(new BigNumber(10).pow(decimals))
    .toString();
};

export const convertDecToMistForNetwork = (
  value: number | string | BigNumber,
  network: string
) => {
  const nativeToken = getNativeTokenForNetwork(network);
  return new BigNumber(value)
    .multipliedBy(new BigNumber(10).pow(nativeToken.decimals))
    .toString();
};

export const convertMistToDec = (
  value: number | string | BigNumber,
  decimals: number = SUI_DECIMALS
) => {
  return new BigNumber(value)
    .dividedBy(new BigNumber(10).pow(decimals))
    .toString();
};

export const convertMistToDecForNetwork = (
  value: number | string | BigNumber,
  network: string
) => {
  const nativeToken = getNativeTokenForNetwork(network);
  return new BigNumber(value)
    .dividedBy(new BigNumber(10).pow(nativeToken.decimals))
    .toString();
};

export const toTokenAmount = (
  value: number | string | BigNumber,
  decimals: number
) => {
  return new BigNumber(value || 0)
    .dividedBy(new BigNumber(10).pow(decimals))
    .toString();
};
export const validateUrl = (url: string) => {
  const REGEX_HTTP = new RegExp(
    "^(http(s)?:\\/\\/)?" + // protocol
      "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
      "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
      "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
      "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
      "(\\#[-a-z\\d_]*)?$",
    "i"
  ); // fragment locator
  if (!REGEX_HTTP.test(url)) {
    return false;
  }
  return true;
};
export const isValidUrl = (string: string) => {
  try {
    new URL(string);
    return true;
  } catch (err) {
    return false;
  }
};

export const WEBSITE_REGEX =
  /^(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,}|https?:\/\/localhost(:\d+)?\/[^\s]*)$/;

export const getTimeFormatBoots = (time: number) => {
  if (!time) return "";

  const duration = moment.duration(time * 1000);

  if (time <= 86400) {
    const hours = duration.asHours();
    return `${hours}H`;
  }

  const hours = duration.asDays();
  return `${hours}D`;
};

export const isReferralCodeDefault = (referralCode: string, userId: string) => {
  return referralCode === `ref${userId}`;
};

export const emojiToBase64ImageIcon = (emoji: string) => {
  const size = 30;
  const canvas = document.createElement("canvas");
  canvas.width = size;
  canvas.height = size;

  const ctx = canvas.getContext("2d");
  if (!ctx) return "";
  ctx.textAlign = "center";
  ctx.textBaseline = "alphabetic";
  ctx.font = `${size - 10}px serif`;
  const fontSize = size - 10;
  const y = size / 2 + fontSize / 2.8;

  ctx.fillText(emoji, size / 2, y);

  return canvas.toDataURL("image/png");
};

export const isNativeToken = (tokenAddress: string, network: string) => {
  const nativeToken = getNativeTokenForNetwork(network);
  return (
    tokenAddress === nativeToken.full ||
    tokenAddress === nativeToken.short ||
    tokenAddress === nativeToken.wrap
  );
};
